name: Code Coverage

on:
  push:
  pull_request:

env:
  CARGO_TERM_COLOR: always

jobs:
  coverage:
    name: Generate Code Coverage
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Install Rust
        uses: actions-rs/toolchain@v1
        with:
          profile: minimal
          toolchain: stable
          override: true
      
      - name: Install cargo-tarpaulin
        uses: actions-rs/install@v0.1
        with:
          crate: cargo-tarpaulin
          version: latest
          use-tool-cache: true
      
      - name: Cache dependencies
        uses: actions/cache@v3
        with:
          path: |
            ~/.cargo/registry
            ~/.cargo/git
            target
          key: ${{ runner.os }}-cargo-coverage-${{ hashFiles('**/Cargo.lock') }}
          restore-keys: |
            ${{ runner.os }}-cargo-coverage-
      
      - name: Generate coverage report
        uses: actions-rs/cargo@v1
        with:
          command: tarpaulin
          args: --verbose --workspace --timeout 120 --out Xml
      
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          file: ./cobertura.xml
          fail_ci_if_error: false