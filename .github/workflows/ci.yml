name: Rust CI

on:
  push:
  pull_request:

env:
  CARGO_TERM_COLOR: always

jobs:
  build:
    name: Build and Test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Install Rust
        uses: actions-rs/toolchain@v1
        with:
          profile: minimal
          toolchain: stable
          override: true
          components: rustfmt, clippy
      
      - name: Cache dependencies
        uses: actions/cache@v3
        with:
          path: |
            ~/.cargo/registry
            ~/.cargo/git
            target
          key: ${{ runner.os }}-cargo-${{ hashFiles('**/Cargo.lock') }}
          restore-keys: |
            ${{ runner.os }}-cargo-
      
      - name: Check formatting
        uses: actions-rs/cargo@v1
        with:
          command: fmt
          args: --all -- --check
      
      - name: <PERSON><PERSON><PERSON>
        uses: actions-rs/cargo@v1
        with:
          command: clippy
          args: -- -D warnings
      
      - name: Build
        uses: actions-rs/cargo@v1
        with:
          command: build
          args: --verbose
      
      - name: Run tests
        uses: actions-rs/cargo@v1
        with:
          command: test
          args: --verbose