name: Create GitHub Release

on:
  push:
    tags:
      - 'v*'

jobs:
  create-release:
    name: Create GitHub Release
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0
      
      - name: Get version from tag
        id: get_version
        run: echo "VERSION=${GITHUB_REF#refs/tags/v}" >> $GITHUB_OUTPUT
      
      - name: Get changelog entry
        id: changelog_reader
        uses: mindsers/changelog-reader-action@v2
        with:
          version: ${{ steps.get_version.outputs.VERSION }}
          path: ./CHANGELOG.md
      
      - name: Create Release
        uses: softprops/action-gh-release@v1
        with:
          name: Release v${{ steps.get_version.outputs.VERSION }}
          body: ${{ steps.changelog_reader.outputs.changes }}
          draft: false
          prerelease: false
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}