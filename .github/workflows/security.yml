name: Security Scan

on:
  push:
  pull_request:
  schedule:
    - cron: '0 0 * * 0'  # Run weekly on Sundays

jobs:
  security_audit:
    name: Security Audit
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Install Rust
        uses: actions-rs/toolchain@v1
        with:
          profile: minimal
          toolchain: stable
          override: true
      
      - name: Run cargo-audit
        uses: rustsec/audit-check@v1
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

  cargo_deny:
    name: Cargo Deny Check
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Run cargo-deny
        uses: EmbarkStudios/cargo-deny-action@v1
        with:
          arguments: --all-features