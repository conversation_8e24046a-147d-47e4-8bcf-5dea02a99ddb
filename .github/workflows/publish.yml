name: Publish

on:
  release:
    types: [created]

jobs:
  publish:
    name: Publish to crates.io
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Install Rust
        uses: actions-rs/toolchain@v1
        with:
          profile: minimal
          toolchain: stable
          override: true
      
      - name: Cache dependencies
        uses: actions/cache@v3
        with:
          path: |
            ~/.cargo/registry
            ~/.cargo/git
            target
          key: ${{ runner.os }}-cargo-publish-${{ hashFiles('**/Cargo.lock') }}
          restore-keys: |
            ${{ runner.os }}-cargo-publish-
      
      - name: Run tests
        uses: actions-rs/cargo@v1
        with:
          command: test
          args: --verbose
      
      - name: Publish to crates.io
        uses: actions-rs/cargo@v1
        with:
          command: publish
          args: --token ${{ secrets.CRATES_IO_TOKEN }}