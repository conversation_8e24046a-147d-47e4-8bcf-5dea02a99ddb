use serde_json::json;
use std::collections::HashMap;
use tempfile::tempdir;
use zinit_client::{Result, ZinitClient};

// Import the mock server types from the tests module
mod mock_server {
    include!("../tests/mock_server.rs");
}
use mock_server::{MockService, MockServiceState, MockServiceTarget, MockZinitServer};

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize logging
    tracing_subscriber::fmt::init();

    println!("Starting service CRUD operations example with mock server");

    // Create a temporary directory for the socket
    let temp_dir = tempdir().expect("Failed to create temp dir");
    let socket_path = temp_dir.path().join("mock-zinit.sock");

    println!("Using socket path: {:?}", socket_path);

    // Create and start the mock server
    let mut server = MockZinitServer::new(&socket_path).await;
    server.start().await.expect("Failed to start mock server");

    // Add some existing mock services
    // Note: In real Zinit, PIDs are automatically assigned by the system when services start
    server.add_service(MockService {
        name: "existing-service".to_string(),
        pid: 1001, // This is just mock data - real PIDs are assigned automatically
        state: MockServiceState::Running,
        target: MockServiceTarget::Up,
        after: HashMap::new(),
    });

    // Create a client to connect to the mock server
    let client = ZinitClient::new(&socket_path);

    println!("\n=== Service CRUD Operations Demo ===\n");

    // 1. CREATE SERVICE
    println!("1. Creating a new service...");
    // Note: Service configuration only includes execution details
    // PIDs are automatically assigned by the system when the service starts
    let service_config = json!({
        "exec": "/usr/bin/my-app",
        "args": ["--port", "8080"],
        "env": {
            "PORT": "8080",
            "ENV": "production"
        },
        "oneshot": false,
        "working_dir": "/opt/my-app"
    });

    match client
        .create_service("my-new-service", service_config)
        .await
    {
        Ok(_) => println!("✓ Service 'my-new-service' created successfully"),
        Err(e) => println!("✗ Failed to create service: {}", e),
    }

    // 2. GET SERVICE
    println!("\n2. Getting service configuration...");
    match client.get_service("existing-service").await {
        Ok(config) => {
            println!("✓ Retrieved service configuration:");
            println!("   {}", serde_json::to_string_pretty(&config).unwrap());
        }
        Err(e) => println!("✗ Failed to get service: {}", e),
    }

    // Try to get the newly created service
    match client.get_service("my-new-service").await {
        Ok(config) => {
            println!("✓ Retrieved newly created service configuration:");
            println!("   {}", serde_json::to_string_pretty(&config).unwrap());
        }
        Err(e) => println!("✗ Failed to get newly created service: {}", e),
    }

    // 3. LIST SERVICES (to show all services)
    println!("\n3. Listing all services...");
    match client.list().await {
        Ok(services) => {
            println!("✓ Current services:");
            for (name, state) in &services {
                println!("   - {}: {:?}", name, state);
            }
        }
        Err(e) => println!("✗ Failed to list services: {}", e),
    }

    // 4. DELETE SERVICE
    println!("\n4. Deleting the newly created service...");
    match client.delete_service("my-new-service").await {
        Ok(_) => println!("✓ Service 'my-new-service' deleted successfully"),
        Err(e) => println!("✗ Failed to delete service: {}", e),
    }

    // 5. VERIFY DELETION
    println!("\n5. Verifying service deletion...");
    match client.get_service("my-new-service").await {
        Ok(_) => println!("✗ Service still exists (unexpected)"),
        Err(e) => println!("✓ Service no longer exists: {}", e),
    }

    // List services again to confirm
    match client.list().await {
        Ok(services) => {
            println!("✓ Services after deletion:");
            for (name, state) in &services {
                println!("   - {}: {:?}", name, state);
            }
        }
        Err(e) => println!("✗ Failed to list services: {}", e),
    }

    // 6. ADVANCED SERVICE CREATION
    println!("\n6. Creating a more complex service...");
    let complex_service_config = json!({
        "exec": "/usr/bin/nginx",
        "args": ["-c", "/etc/nginx/nginx.conf"],
        "env": {
            "NGINX_PORT": "80",
            "NGINX_WORKER_PROCESSES": "auto"
        },
        "oneshot": false,
        "working_dir": "/var/www",
        "after": ["network", "filesystem"],
        "test": "curl -f http://localhost:80/health || exit 1"
    });

    match client
        .create_service("web-server", complex_service_config)
        .await
    {
        Ok(_) => {
            println!("✓ Complex service 'web-server' created successfully");

            // Get the service to show its configuration
            match client.get_service("web-server").await {
                Ok(config) => {
                    println!("   Configuration:");
                    println!("   {}", serde_json::to_string_pretty(&config).unwrap());
                }
                Err(e) => println!("   Failed to retrieve config: {}", e),
            }
        }
        Err(e) => println!("✗ Failed to create complex service: {}", e),
    }

    // Stop the mock server
    println!("\nStopping mock server");
    server.stop().await;

    println!("\n=== Service CRUD Operations Demo Completed ===");
    Ok(())
}
