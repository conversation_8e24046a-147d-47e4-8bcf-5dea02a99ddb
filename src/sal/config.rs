//! Service configuration builders and helpers for the SAL

use crate::sal::error::{<PERSON><PERSON><PERSON><PERSON>, Sal<PERSON><PERSON><PERSON>};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// Service configuration for creating new services
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceConfig {
    /// Service name
    pub name: String,
    /// Executable command
    pub exec: String,
    /// Command line arguments
    #[serde(skip_serializing_if = "Vec::is_empty")]
    pub args: Vec<String>,
    /// Environment variables
    #[serde(skip_serializing_if = "HashMap::is_empty")]
    pub env: HashMap<String, String>,
    /// Working directory
    #[serde(skip_serializing_if = "Option::is_none")]
    pub cwd: Option<String>,
    /// Whether this is a oneshot service (runs once and exits)
    pub oneshot: bool,
    /// Test command to verify service health
    #[serde(skip_serializing_if = "Option::is_none")]
    pub test: Option<String>,
    /// Services this service depends on
    #[serde(skip_serializing_if = "Vec::is_empty")]
    pub after: Vec<String>,
    /// Log file path (optional)
    #[serde(skip_serializing_if = "Option::is_none")]
    pub log: Option<String>,
}

impl ServiceConfig {
    /// Create a new service configuration builder
    pub fn new(name: impl Into<String>, exec: impl Into<String>) -> ServiceConfigBuilder {
        ServiceConfigBuilder::new(name, exec)
    }

    /// Convert to JSON value for Zinit API
    pub fn to_json(&self) -> SalResult<serde_json::Value> {
        serde_json::to_value(self).map_err(SalError::from)
    }

    /// Validate the configuration
    pub fn validate(&self) -> SalResult<()> {
        if self.name.is_empty() {
            return Err(SalError::InvalidConfiguration(
                "Service name cannot be empty".to_string(),
            ));
        }

        if self.exec.is_empty() {
            return Err(SalError::InvalidConfiguration(
                "Executable command cannot be empty".to_string(),
            ));
        }

        // Validate service name (should not contain spaces or special characters)
        if self.name.contains(' ') || self.name.contains('\t') || self.name.contains('\n') {
            return Err(SalError::InvalidConfiguration(
                "Service name cannot contain whitespace".to_string(),
            ));
        }

        Ok(())
    }
}

/// Builder for creating service configurations
#[derive(Debug, Clone)]
pub struct ServiceConfigBuilder {
    name: String,
    exec: String,
    args: Vec<String>,
    env: HashMap<String, String>,
    cwd: Option<String>,
    oneshot: bool,
    test: Option<String>,
    after: Vec<String>,
    log: Option<String>,
}

impl ServiceConfigBuilder {
    /// Create a new builder
    pub fn new(name: impl Into<String>, exec: impl Into<String>) -> Self {
        Self {
            name: name.into(),
            exec: exec.into(),
            args: Vec::new(),
            env: HashMap::new(),
            cwd: None,
            oneshot: false,
            test: None,
            after: Vec::new(),
            log: None,
        }
    }

    /// Add command line arguments
    pub fn with_args<I, S>(mut self, args: I) -> Self
    where
        I: IntoIterator<Item = S>,
        S: Into<String>,
    {
        self.args = args.into_iter().map(|s| s.into()).collect();
        self
    }

    /// Add a single command line argument
    pub fn with_arg(mut self, arg: impl Into<String>) -> Self {
        self.args.push(arg.into());
        self
    }

    /// Set environment variables
    pub fn with_environment(mut self, env: HashMap<String, String>) -> Self {
        self.env = env;
        self
    }

    /// Add a single environment variable
    pub fn with_env(mut self, key: impl Into<String>, value: impl Into<String>) -> Self {
        self.env.insert(key.into(), value.into());
        self
    }

    /// Set working directory
    pub fn with_working_directory(mut self, cwd: impl Into<String>) -> Self {
        self.cwd = Some(cwd.into());
        self
    }

    /// Set whether this is a oneshot service
    pub fn with_oneshot(mut self, oneshot: bool) -> Self {
        self.oneshot = oneshot;
        self
    }

    /// Set test command
    pub fn with_test_command(mut self, test: impl Into<String>) -> Self {
        self.test = Some(test.into());
        self
    }

    /// Add service dependencies
    pub fn with_dependencies<I, S>(mut self, after: I) -> Self
    where
        I: IntoIterator<Item = S>,
        S: Into<String>,
    {
        self.after = after.into_iter().map(|s| s.into()).collect();
        self
    }

    /// Add a single service dependency
    pub fn with_dependency(mut self, service: impl Into<String>) -> Self {
        self.after.push(service.into());
        self
    }

    /// Set log file path
    pub fn with_log_file(mut self, log: impl Into<String>) -> Self {
        self.log = Some(log.into());
        self
    }

    /// Build the service configuration
    pub fn build(self) -> ServiceConfig {
        ServiceConfig {
            name: self.name,
            exec: self.exec,
            args: self.args,
            env: self.env,
            cwd: self.cwd,
            oneshot: self.oneshot,
            test: self.test,
            after: self.after,
            log: self.log,
        }
    }
}

/// Predefined service configuration templates
pub struct ServiceTemplates;

impl ServiceTemplates {
    /// Create a web server service configuration
    pub fn web_server(name: impl Into<String>, exec: impl Into<String>, port: u16) -> ServiceConfigBuilder {
        ServiceConfigBuilder::new(name, exec)
            .with_env("PORT", port.to_string())
            .with_oneshot(false)
    }

    /// Create a database service configuration
    pub fn database(name: impl Into<String>, exec: impl Into<String>, data_dir: impl Into<String>) -> ServiceConfigBuilder {
        ServiceConfigBuilder::new(name, exec)
            .with_working_directory(data_dir)
            .with_oneshot(false)
    }

    /// Create a oneshot initialization service
    pub fn init_script(name: impl Into<String>, exec: impl Into<String>) -> ServiceConfigBuilder {
        ServiceConfigBuilder::new(name, exec)
            .with_oneshot(true)
    }

    /// Create a background daemon service
    pub fn daemon(name: impl Into<String>, exec: impl Into<String>) -> ServiceConfigBuilder {
        ServiceConfigBuilder::new(name, exec)
            .with_oneshot(false)
    }
}
