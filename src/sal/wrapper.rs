//! Main wrapper implementation for the System Abstraction Layer (SAL)

use crate::sal::config::ServiceConfig;
use crate::sal::error::{Sal<PERSON><PERSON><PERSON>, SalR<PERSON>ult};
use crate::{LogEntry, LogStream, ServiceState, ServiceStatus, ZinitClient};
use once_cell::sync::OnceCell;
use std::collections::HashMap;
use std::path::Path;
use std::sync::Arc;
use std::time::Duration;
use tokio::time::timeout;
use tracing::{debug, error, info, warn};

/// Global client instance
static CLIENT: OnceCell<Arc<ZinitClient>> = OnceCell::new();

/// System Abstraction Layer for Zinit Service Management
///
/// This struct provides a high-level interface for managing services through Zinit.
/// It maintains a global, lazily-initialized client instance and provides comprehensive
/// service management functionality.
pub struct ServiceManager;

impl ServiceManager {
    /// Initialize the service manager with the default socket path
    ///
    /// # Example
    /// ```rust,no_run
    /// use zinit_client::sal::ServiceManager;
    ///
    /// #[tokio::main]
    /// async fn main() -> Result<(), Box<dyn std::error::Error>> {
    ///     ServiceManager::init().await?;
    ///     // Now you can use other ServiceManager methods
    ///     Ok(())
    /// }
    /// ```
    pub async fn init() -> SalResult<()> {
        Self::init_with_socket("/var/run/zinit.sock").await
    }

    /// Initialize the service manager with a custom socket path
    ///
    /// # Arguments
    /// * `socket_path` - Path to the Zinit Unix socket
    ///
    /// # Example
    /// ```rust,no_run
    /// use zinit_client::sal::ServiceManager;
    ///
    /// #[tokio::main]
    /// async fn main() -> Result<(), Box<dyn std::error::Error>> {
    ///     ServiceManager::init_with_socket("/custom/path/zinit.sock").await?;
    ///     Ok(())
    /// }
    /// ```
    pub async fn init_with_socket(socket_path: impl AsRef<Path>) -> SalResult<()> {
        let client = ZinitClient::new(socket_path);

        // Test the connection by trying to list services
        client.list().await.map_err(|e| {
            error!("Failed to connect to Zinit: {}", e);
            SalError::from(e)
        })?;

        CLIENT
            .set(Arc::new(client))
            .map_err(|_| SalError::ServiceOperationFailed {
                service: "system".to_string(),
                reason: "Service manager already initialized".to_string(),
            })?;

        info!("Service manager initialized successfully");
        Ok(())
    }

    /// Get the global client instance
    fn client() -> SalResult<&'static Arc<ZinitClient>> {
        CLIENT.get().ok_or(SalError::NotInitialized)
    }

    /// List all services and their states
    ///
    /// # Returns
    /// A HashMap mapping service names to their current states
    ///
    /// # Example
    /// ```rust,no_run
    /// use zinit_client::sal::ServiceManager;
    ///
    /// #[tokio::main]
    /// async fn main() -> Result<(), Box<dyn std::error::Error>> {
    ///     ServiceManager::init().await?;
    ///     let services = ServiceManager::list_services().await?;
    ///     for (name, state) in services {
    ///         println!("Service '{}' is in state: {:?}", name, state);
    ///     }
    ///     Ok(())
    /// }
    /// ```
    pub async fn list_services() -> SalResult<HashMap<String, ServiceState>> {
        debug!("Listing all services");
        let client = Self::client()?;
        client.list().await.map_err(SalError::from)
    }

    /// Get detailed status information for a service
    ///
    /// # Arguments
    /// * `service_name` - Name of the service
    ///
    /// # Returns
    /// Detailed service status including PID, state, target, and dependencies
    ///
    /// # Example
    /// ```rust,no_run
    /// use zinit_client::sal::ServiceManager;
    ///
    /// #[tokio::main]
    /// async fn main() -> Result<(), Box<dyn std::error::Error>> {
    ///     ServiceManager::init().await?;
    ///     let status = ServiceManager::get_service_status("nginx").await?;
    ///     println!("Nginx PID: {}, State: {:?}", status.pid, status.state);
    ///     Ok(())
    /// }
    /// ```
    pub async fn get_service_status(service_name: impl AsRef<str>) -> SalResult<ServiceStatus> {
        let service_name = service_name.as_ref();
        debug!("Getting status for service: {}", service_name);
        let client = Self::client()?;
        client.status(service_name).await.map_err(|e| {
            if matches!(e, crate::ZinitError::UnknownService(_)) {
                SalError::ServiceNotFound(service_name.to_string())
            } else {
                SalError::from(e)
            }
        })
    }

    /// Start a service
    ///
    /// # Arguments
    /// * `service_name` - Name of the service to start
    ///
    /// # Example
    /// ```rust,no_run
    /// use zinit_client::sal::ServiceManager;
    ///
    /// #[tokio::main]
    /// async fn main() -> Result<(), Box<dyn std::error::Error>> {
    ///     ServiceManager::init().await?;
    ///     ServiceManager::start_service("nginx").await?;
    ///     println!("Nginx started successfully");
    ///     Ok(())
    /// }
    /// ```
    pub async fn start_service(service_name: impl AsRef<str>) -> SalResult<()> {
        let service_name = service_name.as_ref();
        info!("Starting service: {}", service_name);
        let client = Self::client()?;
        client.start(service_name).await.map_err(|e| {
            error!("Failed to start service '{}': {}", service_name, e);
            SalError::service_operation_failed(service_name, format!("Start failed: {}", e))
        })
    }

    /// Stop a service
    ///
    /// # Arguments
    /// * `service_name` - Name of the service to stop
    ///
    /// # Example
    /// ```rust,no_run
    /// use zinit_client::sal::ServiceManager;
    ///
    /// #[tokio::main]
    /// async fn main() -> Result<(), Box<dyn std::error::Error>> {
    ///     ServiceManager::init().await?;
    ///     ServiceManager::stop_service("nginx").await?;
    ///     println!("Nginx stopped successfully");
    ///     Ok(())
    /// }
    /// ```
    pub async fn stop_service(service_name: impl AsRef<str>) -> SalResult<()> {
        let service_name = service_name.as_ref();
        info!("Stopping service: {}", service_name);
        let client = Self::client()?;
        client.stop(service_name).await.map_err(|e| {
            error!("Failed to stop service '{}': {}", service_name, e);
            SalError::service_operation_failed(service_name, format!("Stop failed: {}", e))
        })
    }

    /// Restart a service (stop then start)
    ///
    /// # Arguments
    /// * `service_name` - Name of the service to restart
    ///
    /// # Example
    /// ```rust,no_run
    /// use zinit_client::sal::ServiceManager;
    ///
    /// #[tokio::main]
    /// async fn main() -> Result<(), Box<dyn std::error::Error>> {
    ///     ServiceManager::init().await?;
    ///     ServiceManager::restart_service("nginx").await?;
    ///     println!("Nginx restarted successfully");
    ///     Ok(())
    /// }
    /// ```
    pub async fn restart_service(service_name: impl AsRef<str>) -> SalResult<()> {
        let service_name = service_name.as_ref();
        info!("Restarting service: {}", service_name);
        let client = Self::client()?;
        client.restart(service_name).await.map_err(|e| {
            error!("Failed to restart service '{}': {}", service_name, e);
            SalError::service_operation_failed(service_name, format!("Restart failed: {}", e))
        })
    }

    /// Monitor a service (enable automatic restart on failure)
    ///
    /// # Arguments
    /// * `service_name` - Name of the service to monitor
    ///
    /// # Example
    /// ```rust,no_run
    /// use zinit_client::sal::ServiceManager;
    ///
    /// #[tokio::main]
    /// async fn main() -> Result<(), Box<dyn std::error::Error>> {
    ///     ServiceManager::init().await?;
    ///     ServiceManager::monitor_service("nginx").await?;
    ///     println!("Nginx is now being monitored");
    ///     Ok(())
    /// }
    /// ```
    pub async fn monitor_service(service_name: impl AsRef<str>) -> SalResult<()> {
        let service_name = service_name.as_ref();
        info!("Monitoring service: {}", service_name);
        let client = Self::client()?;
        client.monitor(service_name).await.map_err(|e| {
            error!("Failed to monitor service '{}': {}", service_name, e);
            SalError::service_operation_failed(service_name, format!("Monitor failed: {}", e))
        })
    }

    /// Forget a service (stop monitoring and remove from management)
    ///
    /// # Arguments
    /// * `service_name` - Name of the service to forget
    ///
    /// # Example
    /// ```rust,no_run
    /// use zinit_client::sal::ServiceManager;
    ///
    /// #[tokio::main]
    /// async fn main() -> Result<(), Box<dyn std::error::Error>> {
    ///     ServiceManager::init().await?;
    ///     ServiceManager::forget_service("nginx").await?;
    ///     println!("Nginx is no longer being managed");
    ///     Ok(())
    /// }
    /// ```
    pub async fn forget_service(service_name: impl AsRef<str>) -> SalResult<()> {
        let service_name = service_name.as_ref();
        info!("Forgetting service: {}", service_name);
        let client = Self::client()?;
        client.forget(service_name).await.map_err(|e| {
            error!("Failed to forget service '{}': {}", service_name, e);
            SalError::service_operation_failed(service_name, format!("Forget failed: {}", e))
        })
    }

    /// Send a signal to a service
    ///
    /// # Arguments
    /// * `service_name` - Name of the service
    /// * `signal` - Signal name (e.g., "SIGTERM", "SIGKILL", "SIGUSR1")
    ///
    /// # Example
    /// ```rust,no_run
    /// use zinit_client::sal::ServiceManager;
    ///
    /// #[tokio::main]
    /// async fn main() -> Result<(), Box<dyn std::error::Error>> {
    ///     ServiceManager::init().await?;
    ///     ServiceManager::send_signal("nginx", "SIGTERM").await?;
    ///     println!("SIGTERM sent to nginx");
    ///     Ok(())
    /// }
    /// ```
    pub async fn send_signal(
        service_name: impl AsRef<str>,
        signal: impl AsRef<str>,
    ) -> SalResult<()> {
        let service_name = service_name.as_ref();
        let signal = signal.as_ref();
        info!("Sending signal {} to service: {}", signal, service_name);
        let client = Self::client()?;
        client.kill(service_name, signal).await.map_err(|e| {
            error!(
                "Failed to send signal '{}' to service '{}': {}",
                signal, service_name, e
            );
            SalError::service_operation_failed(
                service_name,
                format!("Signal {} failed: {}", signal, e),
            )
        })
    }

    /// Create a new service with the given configuration
    ///
    /// # Arguments
    /// * `service_name` - Name of the service to create
    /// * `config` - Service configuration
    ///
    /// # Example
    /// ```rust,no_run
    /// use zinit_client::sal::{ServiceManager, ServiceConfig};
    ///
    /// #[tokio::main]
    /// async fn main() -> Result<(), Box<dyn std::error::Error>> {
    ///     ServiceManager::init().await?;
    ///
    ///     let config = ServiceConfig::new("my-app", "/usr/bin/my-app")
    ///         .with_oneshot(false)
    ///         .build();
    ///
    ///     ServiceManager::create_service("my-app", config).await?;
    ///     println!("Service created successfully");
    ///     Ok(())
    /// }
    /// ```
    pub async fn create_service(
        service_name: impl AsRef<str>,
        config: ServiceConfig,
    ) -> SalResult<()> {
        let service_name = service_name.as_ref();
        info!("Creating service: {}", service_name);

        // Validate configuration
        config.validate()?;

        let client = Self::client()?;
        let json_config = config.to_json()?;

        client
            .create_service(service_name, json_config)
            .await
            .map_err(|e| {
                error!("Failed to create service '{}': {}", service_name, e);
                SalError::service_operation_failed(service_name, format!("Create failed: {}", e))
            })
    }

    /// Delete a service (stop it first if running, then forget it)
    ///
    /// # Arguments
    /// * `service_name` - Name of the service to delete
    ///
    /// # Example
    /// ```rust,no_run
    /// use zinit_client::sal::ServiceManager;
    ///
    /// #[tokio::main]
    /// async fn main() -> Result<(), Box<dyn std::error::Error>> {
    ///     ServiceManager::init().await?;
    ///     ServiceManager::delete_service("my-app").await?;
    ///     println!("Service deleted successfully");
    ///     Ok(())
    /// }
    /// ```
    pub async fn delete_service(service_name: impl AsRef<str>) -> SalResult<()> {
        let service_name = service_name.as_ref();
        info!("Deleting service: {}", service_name);
        let client = Self::client()?;
        client.delete_service(service_name).await.map_err(|e| {
            error!("Failed to delete service '{}': {}", service_name, e);
            SalError::service_operation_failed(service_name, format!("Delete failed: {}", e))
        })
    }

    /// Get service logs
    ///
    /// # Arguments
    /// * `follow` - Whether to follow the log stream (true) or get a snapshot (false)
    /// * `service_filter` - Optional service name to filter logs by
    ///
    /// # Returns
    /// A stream of log entries
    ///
    /// # Example
    /// ```rust,no_run
    /// use zinit_client::sal::ServiceManager;
    /// use futures::StreamExt;
    ///
    /// #[tokio::main]
    /// async fn main() -> Result<(), Box<dyn std::error::Error>> {
    ///     ServiceManager::init().await?;
    ///     let mut logs = ServiceManager::get_logs(false, Some("nginx")).await?;
    ///
    ///     while let Some(entry) = logs.next().await {
    ///         match entry {
    ///             Ok(log) => println!("[{}] {}: {}", log.timestamp, log.service, log.message),
    ///             Err(e) => eprintln!("Log error: {}", e),
    ///         }
    ///     }
    ///     Ok(())
    /// }
    /// ```
    pub async fn get_logs(
        follow: bool,
        service_filter: Option<impl AsRef<str>>,
    ) -> SalResult<LogStream> {
        debug!(
            "Getting logs (follow: {}, filter: {:?})",
            follow,
            service_filter.as_ref().map(|s| s.as_ref())
        );
        let client = Self::client()?;
        client.logs(follow, service_filter).await.map_err(|e| {
            error!("Failed to get logs: {}", e);
            SalError::LogStreamError(format!("Failed to get logs: {}", e))
        })
    }

    /// Wait for a service to reach a specific state
    ///
    /// # Arguments
    /// * `service_name` - Name of the service
    /// * `expected_state` - The state to wait for
    /// * `timeout_duration` - Maximum time to wait
    ///
    /// # Returns
    /// The final service status when the expected state is reached
    ///
    /// # Example
    /// ```rust,no_run
    /// use zinit_client::sal::ServiceManager;
    /// use zinit_client::ServiceState;
    /// use std::time::Duration;
    ///
    /// #[tokio::main]
    /// async fn main() -> Result<(), Box<dyn std::error::Error>> {
    ///     ServiceManager::init().await?;
    ///     ServiceManager::start_service("nginx").await?;
    ///
    ///     // Wait up to 30 seconds for nginx to be running
    ///     let status = ServiceManager::wait_for_state("nginx", ServiceState::Running, Duration::from_secs(30)).await?;
    ///     println!("Nginx is now running with PID: {}", status.pid);
    ///     Ok(())
    /// }
    /// ```
    pub async fn wait_for_state(
        service_name: impl AsRef<str>,
        expected_state: ServiceState,
        timeout_duration: Duration,
    ) -> SalResult<ServiceStatus> {
        let service_name = service_name.as_ref();
        debug!(
            "Waiting for service '{}' to reach state: {:?}",
            service_name, expected_state
        );

        let start_time = std::time::Instant::now();
        let timeout_seconds = timeout_duration.as_secs();

        let result = timeout(timeout_duration, async {
            loop {
                match Self::get_service_status(service_name).await {
                    Ok(status) => {
                        if status.state == expected_state {
                            return Ok(status);
                        }
                        // Wait a bit before checking again
                        tokio::time::sleep(Duration::from_millis(500)).await;
                    }
                    Err(e) => {
                        warn!("Error checking service status: {}", e);
                        tokio::time::sleep(Duration::from_secs(1)).await;
                    }
                }
            }
        })
        .await;

        match result {
            Ok(status) => {
                info!(
                    "Service '{}' reached state {:?} after {:?}",
                    service_name,
                    expected_state,
                    start_time.elapsed()
                );
                status
            }
            Err(_) => {
                error!(
                    "Timeout waiting for service '{}' to reach state {:?}",
                    service_name, expected_state
                );
                Err(SalError::service_state_timeout(
                    service_name,
                    format!("{:?}", expected_state),
                    timeout_seconds,
                ))
            }
        }
    }

    /// Check if a service exists
    ///
    /// # Arguments
    /// * `service_name` - Name of the service to check
    ///
    /// # Returns
    /// True if the service exists, false otherwise
    ///
    /// # Example
    /// ```rust,no_run
    /// use zinit_client::sal::ServiceManager;
    ///
    /// #[tokio::main]
    /// async fn main() -> Result<(), Box<dyn std::error::Error>> {
    ///     ServiceManager::init().await?;
    ///
    ///     if ServiceManager::service_exists("nginx").await? {
    ///         println!("Nginx service exists");
    ///     } else {
    ///         println!("Nginx service not found");
    ///     }
    ///     Ok(())
    /// }
    /// ```
    pub async fn service_exists(service_name: impl AsRef<str>) -> SalResult<bool> {
        let service_name = service_name.as_ref();
        match Self::get_service_status(service_name).await {
            Ok(_) => Ok(true),
            Err(SalError::ServiceNotFound(_)) => Ok(false),
            Err(e) => Err(e),
        }
    }
}
