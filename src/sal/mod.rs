//! # System Abstraction Layer (SAL) for Zinit Service Management
//!
//! This module provides a high-level, easy-to-use interface for managing services
//! through the Zinit service manager. It's designed to work seamlessly with both
//! direct Rust code and Rhai scripting environments.
//!
//! ## Features
//!
//! - **Global Client Instance**: Automatically manages a single, shared connection to Zinit
//! - **Comprehensive Service Management**: Start, stop, restart, monitor, and configure services
//! - **Error Handling**: Clean error types with detailed context
//! - **Async Support**: Full async/await support for all operations
//! - **Rhai Integration**: Compatible with Rhai scripting (when feature enabled)
//! - **Configuration Builders**: Easy-to-use builders for service configurations
//!
//! ## Basic Usage
//!
//! ```rust,no_run
//! use zinit_client::sal::{ServiceManager, ServiceConfig};
//!
//! #[tokio::main]
//! async fn main() -> Result<(), Box<dyn std::error::Error>> {
//!     // Initialize the service manager (connects to default socket)
//!     ServiceManager::init().await?;
//!     
//!     // List all services
//!     let services = ServiceManager::list_services().await?;
//!     println!("Found {} services", services.len());
//!     
//!     // Create a new service
//!     let config = ServiceConfig::new("my-app", "/usr/bin/my-app")
//!         .with_oneshot(false)
//!         .build();
//!     
//!     ServiceManager::create_service("my-app", config).await?;
//!     
//!     // Start the service
//!     ServiceManager::start_service("my-app").await?;
//!     
//!     // Get service status
//!     let status = ServiceManager::get_service_status("my-app").await?;
//!     println!("Service status: {:?}", status.state);
//!     
//!     Ok(())
//! }
//! ```
//!
//! ## Advanced Configuration
//!
//! ```rust,no_run
//! use zinit_client::sal::{ServiceManager, ServiceConfig};
//! use std::collections::HashMap;
//!
//! #[tokio::main]
//! async fn main() -> Result<(), Box<dyn std::error::Error>> {
//!     // Initialize with custom socket path
//!     ServiceManager::init_with_socket("/custom/path/zinit.sock").await?;
//!     
//!     // Create a complex service configuration
//!     let mut env = HashMap::new();
//!     env.insert("PORT".to_string(), "8080".to_string());
//!     env.insert("ENV".to_string(), "production".to_string());
//!     
//!     let config = ServiceConfig::new("web-server", "/usr/bin/nginx")
//!         .with_args(vec!["-c", "/etc/nginx/nginx.conf"])
//!         .with_environment(env)
//!         .with_working_directory("/var/www")
//!         .with_oneshot(false)
//!         .build();
//!     
//!     ServiceManager::create_service("web-server", config).await?;
//!     ServiceManager::start_service("web-server").await?;
//!     
//!     Ok(())
//! }
//! ```

pub mod config;
pub mod error;
pub mod wrapper;

#[cfg(feature = "rhai-support")]
pub mod rhai_integration;

// Re-export main types for convenience
pub use config::{ServiceConfig, ServiceConfigBuilder};
pub use error::{SalError, SalResult};
pub use wrapper::ServiceManager;

#[cfg(feature = "rhai-support")]
pub use rhai_integration::register_sal_functions;
