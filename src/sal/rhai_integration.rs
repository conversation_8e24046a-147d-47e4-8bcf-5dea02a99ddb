//! Rhai scripting integration for the System Abstraction Layer (SAL)
//!
//! This module provides functions that can be registered with a Rhai engine
//! to enable service management from Rhai scripts.

use crate::sal::config::{ServiceConfig, ServiceConfigBuilder};
use crate::sal::error::{<PERSON><PERSON><PERSON><PERSON>, SalResult};
use crate::sal::wrapper::ServiceManager;
use crate::{ServiceState, ServiceStatus};
use rhai::{Dynamic, Engine, EvalAltResult, Map};
use std::collections::HashMap;
use std::time::Duration;

/// Register SAL functions with a Rhai engine
///
/// This function registers all the service management functions with the provided
/// Rhai engine, making them available for use in Rhai scripts.
///
/// # Arguments
/// * `engine` - The Rhai engine to register functions with
///
/// # Example
/// ```rust,no_run
/// use rhai::Engine;
/// use zinit_client::sal::register_sal_functions;
///
/// let mut engine = Engine::new();
/// register_sal_functions(&mut engine);
///
/// // Now you can use service management functions in Rhai scripts
/// let script = r#"
///     sal_init();
///     sal_start_service("nginx");
///     let status = sal_get_service_status("nginx");
///     print("Nginx PID: " + status.pid);
/// "#;
///
/// engine.eval::<()>(script).unwrap();
/// ```
pub fn register_sal_functions(engine: &mut Engine) {
    // Service manager initialization
    engine.register_fn("sal_init", rhai_init);
    engine.register_fn("sal_init_with_socket", rhai_init_with_socket);

    // Service listing and status
    engine.register_fn("sal_list_services", rhai_list_services);
    engine.register_fn("sal_get_service_status", rhai_get_service_status);
    engine.register_fn("sal_service_exists", rhai_service_exists);

    // Service control
    engine.register_fn("sal_start_service", rhai_start_service);
    engine.register_fn("sal_stop_service", rhai_stop_service);
    engine.register_fn("sal_restart_service", rhai_restart_service);
    engine.register_fn("sal_monitor_service", rhai_monitor_service);
    engine.register_fn("sal_forget_service", rhai_forget_service);
    engine.register_fn("sal_send_signal", rhai_send_signal);

    // Service configuration and management
    engine.register_fn("sal_create_service", rhai_create_service);
    engine.register_fn("sal_delete_service", rhai_delete_service);

    // Utility functions
    engine.register_fn("sal_wait_for_state", rhai_wait_for_state);

    // Service configuration builder
    engine.register_fn("sal_config_new", rhai_config_new);
    engine.register_fn("sal_config_with_args", rhai_config_with_args);
    engine.register_fn("sal_config_with_env", rhai_config_with_env);
    engine.register_fn("sal_config_with_oneshot", rhai_config_with_oneshot);
    engine.register_fn("sal_config_build", rhai_config_build);
}

// Rhai wrapper functions

fn rhai_init() -> Result<(), Box<EvalAltResult>> {
    let rt = tokio::runtime::Runtime::new().map_err(|e| format!("Failed to create runtime: {}", e))?;
    rt.block_on(ServiceManager::init())
        .map_err(|e| format!("Failed to initialize service manager: {}", e).into())
}

fn rhai_init_with_socket(socket_path: &str) -> Result<(), Box<EvalAltResult>> {
    let rt = tokio::runtime::Runtime::new().map_err(|e| format!("Failed to create runtime: {}", e))?;
    rt.block_on(ServiceManager::init_with_socket(socket_path))
        .map_err(|e| format!("Failed to initialize service manager: {}", e).into())
}

fn rhai_list_services() -> Result<Map, Box<EvalAltResult>> {
    let rt = tokio::runtime::Runtime::new().map_err(|e| format!("Failed to create runtime: {}", e))?;
    let services = rt
        .block_on(ServiceManager::list_services())
        .map_err(|e| format!("Failed to list services: {}", e))?;

    let mut map = Map::new();
    for (name, state) in services {
        map.insert(name.into(), format!("{:?}", state).into());
    }
    Ok(map)
}

fn rhai_get_service_status(service_name: &str) -> Result<Map, Box<EvalAltResult>> {
    let rt = tokio::runtime::Runtime::new().map_err(|e| format!("Failed to create runtime: {}", e))?;
    let status = rt
        .block_on(ServiceManager::get_service_status(service_name))
        .map_err(|e| format!("Failed to get service status: {}", e))?;

    let mut map = Map::new();
    map.insert("name".into(), status.name.into());
    map.insert("pid".into(), (status.pid as i64).into());
    map.insert("state".into(), format!("{:?}", status.state).into());
    map.insert("target".into(), format!("{:?}", status.target).into());

    // Convert dependencies
    let mut deps = Map::new();
    for (dep, state) in status.after {
        deps.insert(dep.into(), state.into());
    }
    map.insert("after".into(), deps.into());

    Ok(map)
}

fn rhai_service_exists(service_name: &str) -> Result<bool, Box<EvalAltResult>> {
    let rt = tokio::runtime::Runtime::new().map_err(|e| format!("Failed to create runtime: {}", e))?;
    rt.block_on(ServiceManager::service_exists(service_name))
        .map_err(|e| format!("Failed to check service existence: {}", e).into())
}

fn rhai_start_service(service_name: &str) -> Result<(), Box<EvalAltResult>> {
    let rt = tokio::runtime::Runtime::new().map_err(|e| format!("Failed to create runtime: {}", e))?;
    rt.block_on(ServiceManager::start_service(service_name))
        .map_err(|e| format!("Failed to start service: {}", e).into())
}

fn rhai_stop_service(service_name: &str) -> Result<(), Box<EvalAltResult>> {
    let rt = tokio::runtime::Runtime::new().map_err(|e| format!("Failed to create runtime: {}", e))?;
    rt.block_on(ServiceManager::stop_service(service_name))
        .map_err(|e| format!("Failed to stop service: {}", e).into())
}

fn rhai_restart_service(service_name: &str) -> Result<(), Box<EvalAltResult>> {
    let rt = tokio::runtime::Runtime::new().map_err(|e| format!("Failed to create runtime: {}", e))?;
    rt.block_on(ServiceManager::restart_service(service_name))
        .map_err(|e| format!("Failed to restart service: {}", e).into())
}

fn rhai_monitor_service(service_name: &str) -> Result<(), Box<EvalAltResult>> {
    let rt = tokio::runtime::Runtime::new().map_err(|e| format!("Failed to create runtime: {}", e))?;
    rt.block_on(ServiceManager::monitor_service(service_name))
        .map_err(|e| format!("Failed to monitor service: {}", e).into())
}

fn rhai_forget_service(service_name: &str) -> Result<(), Box<EvalAltResult>> {
    let rt = tokio::runtime::Runtime::new().map_err(|e| format!("Failed to create runtime: {}", e))?;
    rt.block_on(ServiceManager::forget_service(service_name))
        .map_err(|e| format!("Failed to forget service: {}", e).into())
}

fn rhai_send_signal(service_name: &str, signal: &str) -> Result<(), Box<EvalAltResult>> {
    let rt = tokio::runtime::Runtime::new().map_err(|e| format!("Failed to create runtime: {}", e))?;
    rt.block_on(ServiceManager::send_signal(service_name, signal))
        .map_err(|e| format!("Failed to send signal: {}", e).into())
}

fn rhai_create_service(service_name: &str, config_map: Map) -> Result<(), Box<EvalAltResult>> {
    let rt = tokio::runtime::Runtime::new().map_err(|e| format!("Failed to create runtime: {}", e))?;
    
    // Convert Rhai map to ServiceConfig
    let config = map_to_service_config(service_name, config_map)?;
    
    rt.block_on(ServiceManager::create_service(service_name, config))
        .map_err(|e| format!("Failed to create service: {}", e).into())
}

fn rhai_delete_service(service_name: &str) -> Result<(), Box<EvalAltResult>> {
    let rt = tokio::runtime::Runtime::new().map_err(|e| format!("Failed to create runtime: {}", e))?;
    rt.block_on(ServiceManager::delete_service(service_name))
        .map_err(|e| format!("Failed to delete service: {}", e).into())
}

fn rhai_wait_for_state(service_name: &str, state_str: &str, timeout_secs: i64) -> Result<Map, Box<EvalAltResult>> {
    let rt = tokio::runtime::Runtime::new().map_err(|e| format!("Failed to create runtime: {}", e))?;
    
    let state = match state_str {
        "Running" => ServiceState::Running,
        "Success" => ServiceState::Success,
        "Error" => ServiceState::Error,
        "Stopped" => ServiceState::Unknown, // Map to closest equivalent
        _ => return Err(format!("Invalid service state: {}", state_str).into()),
    };
    
    let timeout = Duration::from_secs(timeout_secs as u64);
    let status = rt
        .block_on(ServiceManager::wait_for_state(service_name, state, timeout))
        .map_err(|e| format!("Failed to wait for service state: {}", e))?;

    // Convert status to Rhai map
    let mut map = Map::new();
    map.insert("name".into(), status.name.into());
    map.insert("pid".into(), (status.pid as i64).into());
    map.insert("state".into(), format!("{:?}", status.state).into());
    map.insert("target".into(), format!("{:?}", status.target).into());
    Ok(map)
}

// Service configuration builder functions for Rhai

fn rhai_config_new(name: &str, exec: &str) -> ServiceConfigBuilder {
    ServiceConfig::new(name, exec)
}

fn rhai_config_with_args(mut builder: ServiceConfigBuilder, args: rhai::Array) -> ServiceConfigBuilder {
    let string_args: Vec<String> = args.into_iter()
        .filter_map(|v| v.into_string().ok())
        .collect();
    builder.with_args(string_args)
}

fn rhai_config_with_env(mut builder: ServiceConfigBuilder, env_map: Map) -> ServiceConfigBuilder {
    let mut env = HashMap::new();
    for (key, value) in env_map {
        if let Ok(value_str) = value.into_string() {
            env.insert(key.to_string(), value_str);
        }
    }
    builder.with_environment(env)
}

fn rhai_config_with_oneshot(mut builder: ServiceConfigBuilder, oneshot: bool) -> ServiceConfigBuilder {
    builder.with_oneshot(oneshot)
}

fn rhai_config_build(builder: ServiceConfigBuilder) -> ServiceConfig {
    builder.build()
}

// Helper function to convert Rhai map to ServiceConfig
fn map_to_service_config(name: &str, map: Map) -> Result<ServiceConfig, Box<EvalAltResult>> {
    let exec = map.get("exec")
        .and_then(|v| v.clone().into_string().ok())
        .ok_or("Missing 'exec' field in service configuration")?;

    let mut builder = ServiceConfig::new(name, exec);

    // Handle optional fields
    if let Some(args) = map.get("args") {
        if let Ok(args_array) = args.clone().try_cast::<rhai::Array>() {
            let string_args: Vec<String> = args_array.into_iter()
                .filter_map(|v| v.into_string().ok())
                .collect();
            builder = builder.with_args(string_args);
        }
    }

    if let Some(oneshot) = map.get("oneshot") {
        if let Ok(oneshot_bool) = oneshot.clone().try_cast::<bool>() {
            builder = builder.with_oneshot(oneshot_bool);
        }
    }

    if let Some(env) = map.get("env") {
        if let Ok(env_map) = env.clone().try_cast::<Map>() {
            let mut env_vars = HashMap::new();
            for (key, value) in env_map {
                if let Ok(value_str) = value.into_string() {
                    env_vars.insert(key.to_string(), value_str);
                }
            }
            builder = builder.with_environment(env_vars);
        }
    }

    Ok(builder.build())
}
