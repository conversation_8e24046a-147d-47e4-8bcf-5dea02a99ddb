//! Error types for the System Abstraction Layer (SAL)

use crate::error::ZinitError;
use thiserror::Error;

/// SAL-specific error types that provide more context than the underlying Zinit errors
#[derive(Debug, Error)]
pub enum SalError {
    /// Service manager is not initialized
    #[error("Service manager is not initialized. Call ServiceManager::init() first.")]
    NotInitialized,

    /// Service configuration is invalid
    #[error("Invalid service configuration: {0}")]
    InvalidConfiguration(String),

    /// Service operation failed
    #[error("Service operation failed for '{service}': {reason}")]
    ServiceOperationFailed { service: String, reason: String },

    /// Service not found
    #[error("Service '{0}' not found")]
    ServiceNotFound(String),

    /// Service already exists
    #[error("Service '{0}' already exists")]
    ServiceAlreadyExists(String),

    /// Service is in wrong state for operation
    #[error("Service '{service}' is in state '{current_state}', expected '{expected_state}'")]
    InvalidServiceState {
        service: String,
        current_state: String,
        expected_state: String,
    },

    /// Timeout waiting for service state change
    #[error("Timeout waiting for service '{service}' to reach state '{expected_state}' (waited {timeout_seconds}s)")]
    ServiceStateTimeout {
        service: String,
        expected_state: String,
        timeout_seconds: u64,
    },

    /// Log streaming error
    #[error("Log streaming error: {0}")]
    LogStreamError(String),

    /// Configuration parsing error
    #[error("Configuration parsing error: {0}")]
    ConfigurationError(String),

    /// Underlying Zinit client error
    #[error("Zinit client error: {0}")]
    ZinitError(ZinitError),

    /// JSON serialization/deserialization error
    #[error("JSON error: {0}")]
    JsonError(#[from] serde_json::Error),

    /// IO error
    #[error("IO error: {0}")]
    IoError(#[from] std::io::Error),
}

impl SalError {
    /// Create a service operation failed error
    pub fn service_operation_failed(service: impl Into<String>, reason: impl Into<String>) -> Self {
        Self::ServiceOperationFailed {
            service: service.into(),
            reason: reason.into(),
        }
    }

    /// Create an invalid service state error
    pub fn invalid_service_state(
        service: impl Into<String>,
        current_state: impl Into<String>,
        expected_state: impl Into<String>,
    ) -> Self {
        Self::InvalidServiceState {
            service: service.into(),
            current_state: current_state.into(),
            expected_state: expected_state.into(),
        }
    }

    /// Create a service state timeout error
    pub fn service_state_timeout(
        service: impl Into<String>,
        expected_state: impl Into<String>,
        timeout_seconds: u64,
    ) -> Self {
        Self::ServiceStateTimeout {
            service: service.into(),
            expected_state: expected_state.into(),
            timeout_seconds,
        }
    }

    /// Check if this error indicates the service manager is not initialized
    pub fn is_not_initialized(&self) -> bool {
        matches!(self, SalError::NotInitialized)
    }

    /// Check if this error indicates a service was not found
    pub fn is_service_not_found(&self) -> bool {
        matches!(self, SalError::ServiceNotFound(_))
    }

    /// Check if this error indicates a service already exists
    pub fn is_service_already_exists(&self) -> bool {
        matches!(self, SalError::ServiceAlreadyExists(_))
    }

    /// Check if this error is related to service state
    pub fn is_service_state_error(&self) -> bool {
        matches!(
            self,
            SalError::InvalidServiceState { .. } | SalError::ServiceStateTimeout { .. }
        )
    }
}

/// Result type for SAL operations
pub type SalResult<T> = std::result::Result<T, SalError>;

/// Convert ZinitError to SalError with additional context
impl From<ZinitError> for SalError {
    fn from(error: ZinitError) -> Self {
        match error {
            ZinitError::UnknownService(service) => SalError::ServiceNotFound(service),
            ZinitError::ServiceAlreadyMonitored(service) => SalError::ServiceOperationFailed {
                service,
                reason: "Service is already being monitored".to_string(),
            },
            ZinitError::ServiceIsUp(service) => SalError::InvalidServiceState {
                service,
                current_state: "Up".to_string(),
                expected_state: "Down".to_string(),
            },
            ZinitError::ServiceIsDown(service) => SalError::InvalidServiceState {
                service,
                current_state: "Down".to_string(),
                expected_state: "Up".to_string(),
            },
            other => SalError::ZinitError(other),
        }
    }
}
