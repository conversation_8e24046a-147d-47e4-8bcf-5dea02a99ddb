[package]
name = "zinit-client"
version = "0.2.0"
edition = "2021"
description = "A Rust client library for interacting with Zinit service manager"
license = "MIT"
repository = "https://github.com/threefoldtech/zinit-client"
documentation = "https://docs.rs/zinit-client"
readme = "README.md"
keywords = ["zinit", "client", "service-manager", "init"]
categories = ["api-bindings", "asynchronous"]

[dependencies]
tokio = { version = "1", features = ["full"] }
serde = { version = "1", features = ["derive"] }
serde_json = "1"
thiserror = "1"
async-trait = "0.1"
futures = "0.3"
tracing = "0.1"
chrono = { version = "0.4", features = ["serde"] }
rand = "0.8"
async-stream = "0.3"

[dev-dependencies]
tokio-test = "0.4"
tempfile = "3"
tracing-subscriber = "0.3"
